'use client'

import { useState, useCallback } from 'react'
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
} from 'reactflow'
import 'reactflow/dist/style.css'
import { Plus, Download, Users, DollarSign } from 'lucide-react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { formatCurrency } from '@/lib/utils'

// Custom node component for family members
const FamilyMemberNode = ({ data }: { data: any }) => {
  return (
    <div className="luxury-card p-4 min-w-[200px] border-2 border-blue-200">
      <div className="flex items-center mb-2">
        <div className="w-10 h-10 rounded-full bg-blue-900 flex items-center justify-center mr-3">
          <span className="text-white text-sm font-medium">
            {data.name.split(' ').map((n: string) => n[0]).join('')}
          </span>
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">{data.name}</h3>
          <p className="text-sm text-gray-500">{data.role}</p>
        </div>
      </div>
      
      {data.dateOfBirth && (
        <p className="text-xs text-gray-600 mb-2">
          Born: {new Date(data.dateOfBirth).toLocaleDateString()}
        </p>
      )}
      
      {data.assets && data.assets.length > 0 && (
        <div className="mt-2 pt-2 border-t border-gray-200">
          <div className="flex items-center text-xs text-gray-600">
            <DollarSign className="h-3 w-3 mr-1" />
            <span>Assets: {data.assets.length}</span>
          </div>
          <div className="text-xs font-medium text-green-600">
            {formatCurrency(data.totalAssetValue || 0)}
          </div>
        </div>
      )}
    </div>
  )
}

// Node types
const nodeTypes = {
  familyMember: FamilyMemberNode,
}

// Initial family tree data
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'familyMember',
    position: { x: 400, y: 100 },
    data: {
      name: 'Robert Johnson',
      role: 'Founder',
      dateOfBirth: '1945-03-15',
      assets: ['Family Trust A', 'Real Estate Portfolio'],
      totalAssetValue: 75000000,
    },
  },
  {
    id: '2',
    type: 'familyMember',
    position: { x: 200, y: 250 },
    data: {
      name: 'Michael Johnson',
      role: 'Heir',
      dateOfBirth: '1975-08-22',
      assets: ['Investment Portfolio'],
      totalAssetValue: 25000000,
    },
  },
  {
    id: '3',
    type: 'familyMember',
    position: { x: 600, y: 250 },
    data: {
      name: 'Sarah Johnson',
      role: 'Heir',
      dateOfBirth: '1978-12-10',
      assets: ['Art Collection', 'Trust Fund B'],
      totalAssetValue: 20000000,
    },
  },
  {
    id: '4',
    type: 'familyMember',
    position: { x: 100, y: 400 },
    data: {
      name: 'Emma Johnson',
      role: 'Heir',
      dateOfBirth: '2005-06-18',
      assets: ['Education Trust'],
      totalAssetValue: 2000000,
    },
  },
  {
    id: '5',
    type: 'familyMember',
    position: { x: 300, y: 400 },
    data: {
      name: 'James Johnson',
      role: 'Heir',
      dateOfBirth: '2008-09-25',
      assets: ['Education Trust'],
      totalAssetValue: 2000000,
    },
  },
]

const initialEdges: Edge[] = [
  { id: 'e1-2', source: '1', target: '2', type: 'smoothstep' },
  { id: 'e1-3', source: '1', target: '3', type: 'smoothstep' },
  { id: 'e2-4', source: '2', target: '4', type: 'smoothstep' },
  { id: 'e2-5', source: '2', target: '5', type: 'smoothstep' },
]

export default function FamilyTreePage() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [showAddMember, setShowAddMember] = useState(false)

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const handleExportPDF = () => {
    // TODO: Implement PDF export functionality
    console.log('Exporting family tree as PDF...')
  }

  const handleAddMember = () => {
    setShowAddMember(true)
  }

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Family Tree</h1>
              <p className="mt-1 text-sm text-gray-500">
                Interactive family tree with asset tracking and relationships
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={handleAddMember}
                className="luxury-button-secondary flex items-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Member
              </button>
              <button
                onClick={handleExportPDF}
                className="luxury-button flex items-center"
              >
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </button>
            </div>
          </div>
        </div>

        {/* Family Tree Visualization */}
        <div className="flex-1 bg-gray-50">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
            fitView
            className="bg-gray-50"
          >
            <Controls />
            <MiniMap />
            <Background variant="dots" gap={12} size={1} />
          </ReactFlow>
        </div>

        {/* Statistics Panel */}
        <div className="bg-white border-t border-gray-200 px-6 py-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
            <div className="flex items-center">
              <Users className="h-5 w-5 text-blue-900 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Family Members</p>
                <p className="text-lg font-semibold text-blue-900">{nodes.length}</p>
              </div>
            </div>
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 text-green-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Total Assets</p>
                <p className="text-lg font-semibold text-green-600">
                  {formatCurrency(
                    nodes.reduce((total, node) => total + (node.data.totalAssetValue || 0), 0)
                  )}
                </p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 bg-yellow-500 rounded mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Generations</p>
                <p className="text-lg font-semibold text-yellow-600">3</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 bg-purple-500 rounded mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Active Trusts</p>
                <p className="text-lg font-semibold text-purple-600">4</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Member Modal - TODO: Implement modal */}
      {showAddMember && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="luxury-card p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Add Family Member</h3>
            <p className="text-sm text-gray-500 mb-4">
              This feature will be implemented in the next phase.
            </p>
            <button
              onClick={() => setShowAddMember(false)}
              className="luxury-button w-full"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </DashboardLayout>
  )
}
