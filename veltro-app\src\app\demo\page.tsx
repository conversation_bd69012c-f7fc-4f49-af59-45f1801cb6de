import Link from "next/link";
import { <PERSON>, ArrowRight, Users, FileText, MessageSquare, TrendingUp } from "lucide-react";

export default function DemoPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-900 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Veltro</h1>
            </div>
            <div className="flex space-x-4">
              <Link href="/" className="luxury-button-secondary">
                Back to Home
              </Link>
              <Link href="/auth/login" className="luxury-button">
                Sign In
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Demo Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-6">
            Experience Veltro Demo
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Explore our comprehensive wealth management platform designed for ultra-high-net-worth families.
            This demo showcases the key features with sample data.
          </p>
          
          <div className="flex justify-center space-x-6">
            <Link href="/dashboard" className="luxury-button text-lg px-8 py-4 flex items-center">
              View Dashboard Demo
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>

        {/* Demo Features */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div className="luxury-card p-8">
            <Users className="h-12 w-12 text-blue-900 mb-4" />
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">Interactive Family Tree</h3>
            <p className="text-gray-600 mb-6">
              Visualize your family structure with our drag-and-drop family tree builder. 
              Track relationships, assets, and generational wealth transfer.
            </p>
            <ul className="space-y-2 text-sm text-gray-600 mb-6">
              <li>• Drag-and-drop interface</li>
              <li>• Asset tracking per family member</li>
              <li>• Export to PDF functionality</li>
              <li>• Multi-generational view</li>
            </ul>
            <Link href="/dashboard/family-tree" className="luxury-button-secondary">
              View Family Tree Demo
            </Link>
          </div>

          <div className="luxury-card p-8">
            <FileText className="h-12 w-12 text-blue-900 mb-4" />
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">Secure Document Vault</h3>
            <p className="text-gray-600 mb-6">
              Store and organize all your important documents with enterprise-grade security 
              and role-based access controls.
            </p>
            <ul className="space-y-2 text-sm text-gray-600 mb-6">
              <li>• Categorized document storage</li>
              <li>• Role-based access controls</li>
              <li>• Advanced search and tagging</li>
              <li>• Secure file sharing</li>
            </ul>
            <Link href="/dashboard/documents" className="luxury-button-secondary">
              View Document Vault Demo
            </Link>
          </div>

          <div className="luxury-card p-8">
            <MessageSquare className="h-12 w-12 text-blue-900 mb-4" />
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">Advisor Communication</h3>
            <p className="text-gray-600 mb-6">
              Secure, encrypted messaging with your trusted advisors. Organize conversations 
              by topic and maintain complete communication history.
            </p>
            <ul className="space-y-2 text-sm text-gray-600 mb-6">
              <li>• End-to-end encrypted messaging</li>
              <li>• Organized conversation threads</li>
              <li>• File sharing capabilities</li>
              <li>• Meeting scheduling integration</li>
            </ul>
            <Link href="/dashboard/chat" className="luxury-button-secondary">
              View Chat Demo
            </Link>
          </div>

          <div className="luxury-card p-8">
            <TrendingUp className="h-12 w-12 text-blue-900 mb-4" />
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">Wealth Analytics</h3>
            <p className="text-gray-600 mb-6">
              Comprehensive dashboard with real-time wealth tracking, asset allocation 
              visualization, and performance analytics.
            </p>
            <ul className="space-y-2 text-sm text-gray-600 mb-6">
              <li>• Real-time net worth tracking</li>
              <li>• Asset allocation charts</li>
              <li>• Performance analytics</li>
              <li>• Custom reporting</li>
            </ul>
            <Link href="/dashboard" className="luxury-button-secondary">
              View Analytics Demo
            </Link>
          </div>
        </div>

        {/* Demo Data Notice */}
        <div className="luxury-card p-8 bg-blue-50 border-blue-200">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <Shield className="h-6 w-6 text-blue-900 mt-1" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-blue-900 mb-2">Demo Environment</h3>
              <p className="text-blue-800 mb-4">
                This is a demonstration environment with sample data. All features are fully functional 
                but use mock data for privacy and security. In the production environment, all data 
                would be encrypted and stored securely.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-700">
                <div>
                  <strong>Sample Family:</strong> Johnson Family (5 members)
                </div>
                <div>
                  <strong>Sample Assets:</strong> $125M total net worth
                </div>
                <div>
                  <strong>Sample Documents:</strong> 5 categorized documents
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Secure Your Family's Legacy?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join the families who trust Veltro with their multi-generational wealth management.
          </p>
          <div className="flex justify-center space-x-6">
            <Link href="/auth/register" className="luxury-button text-lg px-8 py-4">
              Start Your Journey
            </Link>
            <Link href="/contact" className="luxury-button-secondary text-lg px-8 py-4">
              Contact Sales
            </Link>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Shield className="h-6 w-6 mr-2" />
              <span className="text-lg font-semibold">Veltro</span>
            </div>
            <p className="text-gray-400">
              © 2024 Veltro. Secure wealth management for families.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
