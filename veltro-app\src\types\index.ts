export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  avatar?: string
  createdAt: Date
  lastLogin?: Date
}

export type UserRole = 'admin' | 'family_member' | 'advisor' | 'viewer'

export interface FamilyMember {
  id: string
  name: string
  dateOfBirth?: Date
  biography?: string
  role: FamilyRole
  parentId?: string
  children?: FamilyMember[]
  assets?: Asset[]
  avatar?: string
  position?: { x: number; y: number }
}

export type FamilyRole = 'founder' | 'heir' | 'spouse' | 'advisor' | 'trustee'

export interface Asset {
  id: string
  name: string
  type: AssetType
  value: number
  description?: string
  ownerId: string
  documents?: Document[]
  createdAt: Date
  updatedAt: Date
}

export type AssetType = 'real_estate' | 'trust' | 'private_equity' | 'collectibles' | 'cash' | 'securities' | 'business'

export interface Document {
  id: string
  name: string
  type: DocumentType
  category: DocumentCategory
  fileUrl: string
  fileSize: number
  mimeType: string
  uploadedBy: string
  uploadedAt: Date
  tags: string[]
  metadata?: Record<string, any>
  accessLevel: AccessLevel
}

export type DocumentType = 'pdf' | 'doc' | 'docx' | 'image' | 'other'
export type DocumentCategory = 'trusts' | 'wills' | 'deeds' | 'medical' | 'financial' | 'legal' | 'other'
export type AccessLevel = 'admin' | 'family' | 'advisor' | 'restricted'

export interface ChatMessage {
  id: string
  content: string
  senderId: string
  senderName: string
  senderRole: UserRole
  threadId: string
  timestamp: Date
  edited?: boolean
  editedAt?: Date
}

export interface ChatThread {
  id: string
  name: string
  type: ThreadType
  participants: string[]
  createdBy: string
  createdAt: Date
  lastMessage?: ChatMessage
  isArchived: boolean
}

export type ThreadType = 'trust_planning' | 'asset_discussion' | 'meeting_prep' | 'general'

export interface WealthSummary {
  totalNetWorth: number
  assetBreakdown: {
    type: AssetType
    value: number
    percentage: number
  }[]
  monthlyChange: number
  yearlyChange: number
}

export interface Notification {
  id: string
  title: string
  message: string
  type: NotificationType
  userId: string
  read: boolean
  createdAt: Date
  actionUrl?: string
}

export type NotificationType = 'document_added' | 'trust_event' | 'birthday' | 'meeting' | 'system'

export interface TrustEvent {
  id: string
  title: string
  description: string
  date: Date
  type: 'distribution' | 'review' | 'termination' | 'other'
  relatedAssets?: string[]
  participants?: string[]
}
