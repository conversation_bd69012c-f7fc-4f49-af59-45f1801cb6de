'use client'

import { useState } from 'react'
import { 
  Upload, 
  Search, 
  Filter, 
  FileText, 
  Download, 
  Eye, 
  MoreVertical,
  Folder,
  Lock,
  Calendar
} from 'lucide-react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { formatDate, formatRelativeTime } from '@/lib/utils'

// Mock document data
const documents = [
  {
    id: '1',
    name: 'Family Trust Amendment 2024.pdf',
    category: 'trusts',
    type: 'pdf',
    size: 2.4,
    uploadedBy: '<PERSON>',
    uploadedAt: '2024-01-15T10:30:00Z',
    accessLevel: 'admin',
    tags: ['trust', 'amendment', '2024']
  },
  {
    id: '2',
    name: 'Estate Planning Will.pdf',
    category: 'wills',
    type: 'pdf',
    size: 1.8,
    uploadedBy: '<PERSON>',
    uploadedAt: '2024-01-10T14:20:00Z',
    accessLevel: 'family',
    tags: ['will', 'estate', 'planning']
  },
  {
    id: '3',
    name: 'Property Deed - Manhattan.pdf',
    category: 'deeds',
    type: 'pdf',
    size: 3.2,
    uploadedBy: '<PERSON>',
    uploadedAt: '2024-01-08T09:15:00Z',
    accessLevel: 'family',
    tags: ['deed', 'property', 'manhattan']
  },
  {
    id: '4',
    name: 'Medical Records - Robert.pdf',
    category: 'medical',
    type: 'pdf',
    size: 0.9,
    uploadedBy: 'Dr. Smith',
    uploadedAt: '2024-01-05T16:45:00Z',
    accessLevel: 'restricted',
    tags: ['medical', 'records', 'confidential']
  },
  {
    id: '5',
    name: 'Investment Portfolio Q4 2023.pdf',
    category: 'financial',
    type: 'pdf',
    size: 4.1,
    uploadedBy: 'Goldman Sachs',
    uploadedAt: '2024-01-03T11:30:00Z',
    accessLevel: 'advisor',
    tags: ['investment', 'portfolio', 'q4', '2023']
  }
]

const categories = [
  { id: 'all', name: 'All Documents', count: documents.length },
  { id: 'trusts', name: 'Trusts', count: documents.filter(d => d.category === 'trusts').length },
  { id: 'wills', name: 'Wills', count: documents.filter(d => d.category === 'wills').length },
  { id: 'deeds', name: 'Deeds', count: documents.filter(d => d.category === 'deeds').length },
  { id: 'medical', name: 'Medical', count: documents.filter(d => d.category === 'medical').length },
  { id: 'financial', name: 'Financial', count: documents.filter(d => d.category === 'financial').length },
]

const accessLevelColors = {
  admin: 'bg-red-100 text-red-800',
  family: 'bg-blue-100 text-blue-800',
  advisor: 'bg-yellow-100 text-yellow-800',
  restricted: 'bg-gray-100 text-gray-800'
}

export default function DocumentsPage() {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [showUpload, setShowUpload] = useState(false)

  const filteredDocuments = documents.filter(doc => {
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory
    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  const handleUpload = () => {
    setShowUpload(true)
  }

  const handleDownload = (docId: string) => {
    console.log('Downloading document:', docId)
  }

  const handleView = (docId: string) => {
    console.log('Viewing document:', docId)
  }

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          {/* Page header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Document Vault</h1>
                <p className="mt-1 text-sm text-gray-500">
                  Secure storage for all your important documents
                </p>
              </div>
              <button
                onClick={handleUpload}
                className="luxury-button flex items-center"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar - Categories */}
            <div className="lg:col-span-1">
              <div className="luxury-card p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Categories</h3>
                <nav className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-blue-50 text-blue-900 border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center">
                        <Folder className="h-4 w-4 mr-2" />
                        {category.name}
                      </div>
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                        {category.count}
                      </span>
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Main content */}
            <div className="lg:col-span-3">
              {/* Search and filters */}
              <div className="luxury-card p-6 mb-6">
                <div className="flex items-center space-x-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search documents..."
                      className="luxury-input w-full pl-10"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <button className="luxury-button-secondary flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </button>
                </div>
              </div>

              {/* Documents list */}
              <div className="luxury-card">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    Documents ({filteredDocuments.length})
                  </h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {filteredDocuments.map((document) => (
                    <div key={document.id} className="p-6 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center flex-1">
                          <FileText className="h-8 w-8 text-blue-900 mr-4" />
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-gray-900">
                              {document.name}
                            </h4>
                            <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                              <span>{document.size} MB</span>
                              <span>•</span>
                              <span>Uploaded by {document.uploadedBy}</span>
                              <span>•</span>
                              <span>{formatRelativeTime(document.uploadedAt)}</span>
                            </div>
                            <div className="mt-2 flex items-center space-x-2">
                              {document.tags.map((tag) => (
                                <span
                                  key={tag}
                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            accessLevelColors[document.accessLevel as keyof typeof accessLevelColors]
                          }`}>
                            <Lock className="h-3 w-3 mr-1" />
                            {document.accessLevel}
                          </span>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleView(document.id)}
                              className="p-2 text-gray-400 hover:text-gray-600"
                              title="View document"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDownload(document.id)}
                              className="p-2 text-gray-400 hover:text-gray-600"
                              title="Download document"
                            >
                              <Download className="h-4 w-4" />
                            </button>
                            <button className="p-2 text-gray-400 hover:text-gray-600">
                              <MoreVertical className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Upload Modal */}
      {showUpload && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="luxury-card p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Document</h3>
            <p className="text-sm text-gray-500 mb-4">
              This feature will be implemented with Supabase storage integration.
            </p>
            <button
              onClick={() => setShowUpload(false)}
              className="luxury-button w-full"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </DashboardLayout>
  )
}
