import { FamilyMember, Document, Chat<PERSON><PERSON><PERSON>, Chat<PERSON>essage, User, Asset, WealthSummary } from '@/types'

// Mock Users
export const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'admin',
    createdAt: new Date('2023-01-01'),
    lastLogin: new Date('2024-01-15T10:30:00Z')
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'family_member',
    createdAt: new Date('2023-02-15'),
    lastLogin: new Date('2024-01-14T16:45:00Z')
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'family_member',
    createdAt: new Date('2023-03-01'),
    lastLogin: new Date('2024-01-13T09:20:00Z')
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: 'Goldman Sachs Advisor',
    role: 'advisor',
    createdAt: new Date('2023-01-15'),
    lastLogin: new Date('2024-01-15T14:00:00Z')
  },
  {
    id: '5',
    email: '<EMAIL>',
    name: 'Attorney <PERSON>',
    role: 'advisor',
    createdAt: new Date('2023-01-20'),
    lastLogin: new Date('2024-01-15T11:30:00Z')
  }
]

// Mock Assets
export const mockAssets: Asset[] = [
  {
    id: '1',
    name: 'Family Trust A',
    type: 'trust',
    value: 45000000,
    description: 'Primary family trust established in 1995',
    ownerId: '1',
    createdAt: new Date('1995-06-15'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '2',
    name: 'Manhattan Real Estate Portfolio',
    type: 'real_estate',
    value: 25000000,
    description: 'Commercial and residential properties in Manhattan',
    ownerId: '1',
    createdAt: new Date('2000-03-20'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '3',
    name: 'Private Equity Investments',
    type: 'private_equity',
    value: 35000000,
    description: 'Diversified private equity portfolio',
    ownerId: '2',
    createdAt: new Date('2010-08-10'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '4',
    name: 'Art Collection',
    type: 'collectibles',
    value: 8000000,
    description: 'Contemporary and classical art collection',
    ownerId: '3',
    createdAt: new Date('2005-12-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '5',
    name: 'Securities Portfolio',
    type: 'securities',
    value: 15000000,
    description: 'Publicly traded securities and bonds',
    ownerId: '2',
    createdAt: new Date('2015-04-15'),
    updatedAt: new Date('2024-01-01')
  }
]

// Mock Family Members
export const mockFamilyMembers: FamilyMember[] = [
  {
    id: '1',
    name: 'Robert Johnson',
    dateOfBirth: new Date('1945-03-15'),
    biography: 'Founder of Johnson Industries, established the family trust in 1995.',
    role: 'founder',
    assets: mockAssets.filter(a => a.ownerId === '1'),
    position: { x: 400, y: 100 }
  },
  {
    id: '2',
    name: 'Michael Johnson',
    dateOfBirth: new Date('1975-08-22'),
    biography: 'CEO of Johnson Industries, manages the family investment portfolio.',
    role: 'heir',
    parentId: '1',
    assets: mockAssets.filter(a => a.ownerId === '2'),
    position: { x: 200, y: 250 }
  },
  {
    id: '3',
    name: 'Sarah Johnson',
    dateOfBirth: new Date('1978-12-10'),
    biography: 'Art curator and philanthropist, manages the family foundation.',
    role: 'heir',
    parentId: '1',
    assets: mockAssets.filter(a => a.ownerId === '3'),
    position: { x: 600, y: 250 }
  },
  {
    id: '4',
    name: 'Emma Johnson',
    dateOfBirth: new Date('2005-06-18'),
    biography: 'Student at Harvard University, studying economics.',
    role: 'heir',
    parentId: '2',
    position: { x: 100, y: 400 }
  },
  {
    id: '5',
    name: 'James Johnson',
    dateOfBirth: new Date('2008-09-25'),
    biography: 'High school student with interests in technology and entrepreneurship.',
    role: 'heir',
    parentId: '2',
    position: { x: 300, y: 400 }
  }
]

// Mock Documents
export const mockDocuments: Document[] = [
  {
    id: '1',
    name: 'Family Trust Amendment 2024.pdf',
    type: 'pdf',
    category: 'trusts',
    fileUrl: '/documents/trust-amendment-2024.pdf',
    fileSize: 2457600, // 2.4 MB
    mimeType: 'application/pdf',
    uploadedBy: '1',
    uploadedAt: new Date('2024-01-15T10:30:00Z'),
    tags: ['trust', 'amendment', '2024'],
    accessLevel: 'admin'
  },
  {
    id: '2',
    name: 'Estate Planning Will.pdf',
    type: 'pdf',
    category: 'wills',
    fileUrl: '/documents/estate-planning-will.pdf',
    fileSize: 1887436, // 1.8 MB
    mimeType: 'application/pdf',
    uploadedBy: '2',
    uploadedAt: new Date('2024-01-10T14:20:00Z'),
    tags: ['will', 'estate', 'planning'],
    accessLevel: 'family'
  },
  {
    id: '3',
    name: 'Property Deed - Manhattan.pdf',
    type: 'pdf',
    category: 'deeds',
    fileUrl: '/documents/property-deed-manhattan.pdf',
    fileSize: 3355443, // 3.2 MB
    mimeType: 'application/pdf',
    uploadedBy: '3',
    uploadedAt: new Date('2024-01-08T09:15:00Z'),
    tags: ['deed', 'property', 'manhattan'],
    accessLevel: 'family'
  },
  {
    id: '4',
    name: 'Medical Records - Robert.pdf',
    type: 'pdf',
    category: 'medical',
    fileUrl: '/documents/medical-records-robert.pdf',
    fileSize: 943718, // 0.9 MB
    mimeType: 'application/pdf',
    uploadedBy: '5',
    uploadedAt: new Date('2024-01-05T16:45:00Z'),
    tags: ['medical', 'records', 'confidential'],
    accessLevel: 'restricted'
  },
  {
    id: '5',
    name: 'Investment Portfolio Q4 2023.pdf',
    type: 'pdf',
    category: 'financial',
    fileUrl: '/documents/investment-portfolio-q4-2023.pdf',
    fileSize: 4294967, // 4.1 MB
    mimeType: 'application/pdf',
    uploadedBy: '4',
    uploadedAt: new Date('2024-01-03T11:30:00Z'),
    tags: ['investment', 'portfolio', 'q4', '2023'],
    accessLevel: 'advisor'
  }
]

// Mock Chat Messages
export const mockChatMessages: ChatMessage[] = [
  {
    id: '1',
    content: 'Good morning everyone. I wanted to discuss the upcoming trust distribution.',
    senderId: '1',
    senderName: 'John Doe',
    senderRole: 'admin',
    threadId: '1',
    timestamp: new Date('2024-01-15T09:00:00Z')
  },
  {
    id: '2',
    content: 'Thanks for bringing this up, John. I have some questions about the tax implications.',
    senderId: '2',
    senderName: 'Sarah Johnson',
    senderRole: 'family_member',
    threadId: '1',
    timestamp: new Date('2024-01-15T09:15:00Z')
  },
  {
    id: '3',
    content: 'I can help clarify the tax implications. The distribution will be treated as ordinary income for the beneficiaries.',
    senderId: '5',
    senderName: 'Attorney Smith',
    senderRole: 'advisor',
    threadId: '1',
    timestamp: new Date('2024-01-15T09:30:00Z')
  }
]

// Mock Chat Threads
export const mockChatThreads: ChatThread[] = [
  {
    id: '1',
    name: 'Trust Planning Discussion',
    type: 'trust_planning',
    participants: ['1', '2', '5'],
    createdBy: '1',
    createdAt: new Date('2024-01-10T10:00:00Z'),
    lastMessage: mockChatMessages[2],
    isArchived: false
  },
  {
    id: '2',
    name: 'Investment Strategy Q1 2024',
    type: 'asset_discussion',
    participants: ['1', '3', '4'],
    createdBy: '1',
    createdAt: new Date('2024-01-08T14:00:00Z'),
    isArchived: false
  },
  {
    id: '3',
    name: 'Family Meeting Prep',
    type: 'meeting_prep',
    participants: ['1', '2', '3', '4'],
    createdBy: '2',
    createdAt: new Date('2024-01-05T16:00:00Z'),
    isArchived: false
  }
]

// Mock Wealth Summary
export const mockWealthSummary: WealthSummary = {
  totalNetWorth: 125000000,
  monthlyChange: 2.4,
  yearlyChange: 8.7,
  assetBreakdown: [
    { type: 'real_estate', value: 45000000, percentage: 36 },
    { type: 'private_equity', value: 35000000, percentage: 28 },
    { type: 'trust', value: 25000000, percentage: 20 },
    { type: 'securities', value: 15000000, percentage: 12 },
    { type: 'cash', value: 5000000, percentage: 4 }
  ]
}
