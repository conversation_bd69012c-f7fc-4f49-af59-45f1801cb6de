'use client'

import { useState } from 'react'
import { 
  Send, 
  Plus, 
  Search, 
  MoreVertical, 
  Users, 
  MessageSquare,
  Phone,
  Video,
  Paperclip
} from 'lucide-react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { formatRelativeTime } from '@/lib/utils'

// Mock chat data
const chatThreads = [
  {
    id: '1',
    name: 'Trust Planning Discussion',
    type: 'trust_planning',
    participants: ['<PERSON>', '<PERSON>', '<PERSON>'],
    lastMessage: {
      content: 'I\'ve reviewed the trust amendment documents. We should schedule a meeting to discuss the changes.',
      timestamp: '2024-01-15T14:30:00Z',
      sender: '<PERSON>'
    },
    unreadCount: 2
  },
  {
    id: '2',
    name: 'Investment Strategy Q1 2024',
    type: 'asset_discussion',
    participants: ['<PERSON>', '<PERSON>', 'Goldman Sachs Advisor'],
    lastMessage: {
      content: 'The market outlook for Q1 looks promising. Let\'s discuss rebalancing the portfolio.',
      timestamp: '2024-01-14T16:45:00Z',
      sender: '<PERSON> Sachs Advisor'
    },
    unreadCount: 0
  },
  {
    id: '3',
    name: 'Family Meeting Prep',
    type: 'meeting_prep',
    participants: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    lastMessage: {
      content: 'Don\'t forget to bring the quarterly reports to tomorrow\'s meeting.',
      timestamp: '2024-01-13T10:20:00Z',
      sender: 'John Doe'
    },
    unreadCount: 1
  }
]

const messages = [
  {
    id: '1',
    content: 'Good morning everyone. I wanted to discuss the upcoming trust distribution.',
    sender: 'John Doe',
    timestamp: '2024-01-15T09:00:00Z',
    isCurrentUser: true
  },
  {
    id: '2',
    content: 'Thanks for bringing this up, John. I have some questions about the tax implications.',
    sender: 'Sarah Johnson',
    timestamp: '2024-01-15T09:15:00Z',
    isCurrentUser: false
  },
  {
    id: '3',
    content: 'I can help clarify the tax implications. The distribution will be treated as ordinary income for the beneficiaries.',
    sender: 'Attorney Smith',
    timestamp: '2024-01-15T09:30:00Z',
    isCurrentUser: false
  },
  {
    id: '4',
    content: 'That makes sense. Should we schedule a call to go over the details?',
    sender: 'Sarah Johnson',
    timestamp: '2024-01-15T10:00:00Z',
    isCurrentUser: false
  }
]

export default function ChatPage() {
  const [selectedThread, setSelectedThread] = useState(chatThreads[0])
  const [newMessage, setNewMessage] = useState('')
  const [searchQuery, setSearchQuery] = useState('')

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      console.log('Sending message:', newMessage)
      setNewMessage('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const filteredThreads = chatThreads.filter(thread =>
    thread.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    thread.participants.some(p => p.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  return (
    <DashboardLayout>
      <div className="h-full flex">
        {/* Sidebar - Chat threads */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Messages</h2>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Plus className="h-5 w-5" />
              </button>
            </div>
            
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search conversations..."
                className="luxury-input w-full pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Thread list */}
          <div className="flex-1 overflow-y-auto">
            {filteredThreads.map((thread) => (
              <button
                key={thread.id}
                onClick={() => setSelectedThread(thread)}
                className={`w-full p-4 text-left hover:bg-gray-50 border-b border-gray-100 ${
                  selectedThread.id === thread.id ? 'bg-blue-50 border-blue-200' : ''
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900 truncate">{thread.name}</h3>
                  {thread.unreadCount > 0 && (
                    <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                      {thread.unreadCount}
                    </span>
                  )}
                </div>
                <div className="flex items-center text-xs text-gray-500 mb-1">
                  <Users className="h-3 w-3 mr-1" />
                  <span>{thread.participants.length} participants</span>
                </div>
                <p className="text-sm text-gray-600 truncate">
                  {thread.lastMessage.sender}: {thread.lastMessage.content}
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  {formatRelativeTime(thread.lastMessage.timestamp)}
                </p>
              </button>
            ))}
          </div>
        </div>

        {/* Main chat area */}
        <div className="flex-1 flex flex-col">
          {/* Chat header */}
          <div className="bg-white border-b border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{selectedThread.name}</h3>
                <div className="flex items-center text-sm text-gray-500">
                  <Users className="h-4 w-4 mr-1" />
                  <span>{selectedThread.participants.join(', ')}</span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button className="p-2 text-gray-400 hover:text-gray-600">
                  <Phone className="h-5 w-5" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600">
                  <Video className="h-5 w-5" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600">
                  <MoreVertical className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isCurrentUser ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.isCurrentUser
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-900 shadow-sm'
                }`}>
                  {!message.isCurrentUser && (
                    <p className="text-xs font-medium mb-1 text-gray-500">
                      {message.sender}
                    </p>
                  )}
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.isCurrentUser ? 'text-blue-100' : 'text-gray-400'
                  }`}>
                    {formatRelativeTime(message.timestamp)}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Message input */}
          <div className="bg-white border-t border-gray-200 p-4">
            <div className="flex items-end space-x-2">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Paperclip className="h-5 w-5" />
              </button>
              <div className="flex-1">
                <textarea
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  className="luxury-input w-full resize-none"
                  rows={1}
                />
              </div>
              <button
                onClick={handleSendMessage}
                disabled={!newMessage.trim()}
                className="luxury-button p-3 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
