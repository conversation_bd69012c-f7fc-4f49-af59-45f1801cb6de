'use client'

import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  FileText, 
  Calendar,
  DollarSign,
  PieChart,
  Activity
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import DashboardLayout from '@/components/layout/DashboardLayout'

// Mock data for demonstration
const wealthSummary = {
  totalNetWorth: 125000000,
  monthlyChange: 2.4,
  yearlyChange: 8.7,
  assetBreakdown: [
    { type: 'Real Estate', value: 45000000, percentage: 36 },
    { type: 'Private Equity', value: 35000000, percentage: 28 },
    { type: 'Trusts', value: 25000000, percentage: 20 },
    { type: 'Securities', value: 15000000, percentage: 12 },
    { type: 'Cash', value: 5000000, percentage: 4 }
  ]
}

const recentActivities = [
  {
    id: 1,
    type: 'document',
    title: 'Trust Amendment uploaded',
    description: 'Family Trust 2024 Amendment.pdf',
    timestamp: '2 hours ago',
    icon: FileText
  },
  {
    id: 2,
    type: 'family',
    title: 'New family member added',
    description: '<PERSON> added to family tree',
    timestamp: '1 day ago',
    icon: Users
  },
  {
    id: 3,
    type: 'meeting',
    title: 'Advisor meeting scheduled',
    description: 'Q4 Portfolio Review with Goldman Sachs',
    timestamp: '2 days ago',
    icon: Calendar
  }
]

const upcomingEvents = [
  {
    id: 1,
    title: 'Trust Distribution',
    date: '2024-02-15',
    type: 'financial',
    description: 'Annual distribution from Family Trust A'
  },
  {
    id: 2,
    title: 'Estate Planning Review',
    date: '2024-02-20',
    type: 'meeting',
    description: 'Quarterly review with estate planning attorney'
  },
  {
    id: 3,
    title: 'Investment Committee Meeting',
    date: '2024-02-25',
    type: 'meeting',
    description: 'Review portfolio allocation and strategy'
  }
]

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          {/* Page header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="mt-1 text-sm text-gray-500">
              Welcome back, John. Here's what's happening with your wealth.
            </p>
          </div>

          {/* Wealth Summary Cards */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {/* Total Net Worth */}
            <div className="luxury-card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-blue-900" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Net Worth
                    </dt>
                    <dd className="text-lg font-semibold text-gray-900">
                      {formatCurrency(wealthSummary.totalNetWorth)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            {/* Monthly Change */}
            <div className="luxury-card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-green-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Monthly Change
                    </dt>
                    <dd className="text-lg font-semibold text-green-600">
                      +{wealthSummary.monthlyChange}%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            {/* Yearly Change */}
            <div className="luxury-card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Activity className="h-8 w-8 text-blue-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Yearly Change
                    </dt>
                    <dd className="text-lg font-semibold text-blue-600">
                      +{wealthSummary.yearlyChange}%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            {/* Asset Classes */}
            <div className="luxury-card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <PieChart className="h-8 w-8 text-purple-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Asset Classes
                    </dt>
                    <dd className="text-lg font-semibold text-gray-900">
                      {wealthSummary.assetBreakdown.length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Asset Breakdown */}
            <div className="luxury-card p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Asset Breakdown</h3>
              <div className="space-y-4">
                {wealthSummary.assetBreakdown.map((asset, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-blue-900 mr-3" style={{
                        backgroundColor: `hsl(${index * 60}, 70%, 50%)`
                      }} />
                      <span className="text-sm font-medium text-gray-900">{asset.type}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold text-gray-900">
                        {formatCurrency(asset.value)}
                      </div>
                      <div className="text-xs text-gray-500">{asset.percentage}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Recent Activity */}
            <div className="luxury-card p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-start">
                    <div className="flex-shrink-0">
                      <activity.icon className="h-5 w-5 text-gray-400" />
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-sm text-gray-500">{activity.description}</p>
                      <p className="text-xs text-gray-400 mt-1">{activity.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Upcoming Events */}
            <div className="luxury-card p-6 lg:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Upcoming Events</h3>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">{event.title}</span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        event.type === 'financial' 
                          ? 'bg-green-100 text-green-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {event.type}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500 mb-2">{event.description}</p>
                    <p className="text-xs text-gray-400">{new Date(event.date).toLocaleDateString()}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
